<% content_for :page_title, t('admin.companies.title', default: 'Company Management') %>
<% content_for :page_subtitle, t('admin.companies.subtitle', default: 'Manage companies, owners, and subscriptions') %>

<!-- Companies Summary -->
<div class="stats-grid" style="margin-bottom: 2rem;">
  <div class="stat-card">
    <div class="stat-value"><%= @total_companies %></div>
    <div class="stat-label"><%= t('admin.companies.total_companies', default: 'Total Companies') %></div>
  </div>
  
  <div class="stat-card">
    <div class="stat-value"><%= @companies.count { |c| c[:plan]&.name == 'premium' } %></div>
    <div class="stat-label"><%= t('admin.companies.premium_companies', default: 'Premium Companies') %></div>
  </div>
  
  <div class="stat-card">
    <div class="stat-value"><%= @companies.count { |c| c[:plan]&.name == 'plus' } %></div>
    <div class="stat-label"><%= t('admin.companies.plus_companies', default: 'Plus Companies') %></div>
  </div>
  
  <div class="stat-card">
    <div class="stat-value"><%= @companies.count { |c| c[:plan].nil? } %></div>
    <div class="stat-label"><%= t('admin.companies.free_companies', default: 'Free Companies') %></div>
  </div>
</div>

<!-- Companies Table -->
<div class="data-table">
  <div class="table-header">
    <h3><%= t('admin.companies.all_companies', default: 'All Companies') %></h3>
  </div>
  
  <div style="padding: 1rem 1.5rem; background: #f9fafb; border-bottom: 1px solid #e5e7eb;">
    <div style="display: grid; grid-template-columns: 2fr 2fr 1fr 1fr 1fr 1.5fr; gap: 1rem; font-weight: 600; font-size: 0.875rem; color: #374151;">
      <div><%= t('admin.companies.company_name', default: 'Company Name') %></div>
      <div><%= t('admin.companies.owner', default: 'Owner') %></div>
      <div style="text-align: center;"><%= t('admin.companies.employees', default: 'Employees') %></div>
      <div style="text-align: center;"><%= t('admin.companies.plan', default: 'Plan') %></div>
      <div style="text-align: center;"><%= t('admin.companies.status', default: 'Status') %></div>
      <div style="text-align: center;"><%= t('admin.companies.actions', default: 'Actions') %></div>
    </div>
  </div>
  
  <div class="table-content" style="max-height: 600px;">
    <% if @companies.any? %>
      <% @companies.each do |company| %>
        <div class="table-row" style="padding: 1rem 1.5rem;">
          <div style="display: grid; grid-template-columns: 2fr 2fr 1fr 1fr 1fr 1.5fr; gap: 1rem; align-items: center;">
            <!-- Company Name -->
            <div style="min-width: 0; overflow: hidden;">
              <div style="font-weight: 600; margin-bottom: 0.25rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                <%= link_to company[:name], admin_company_path(company[:id], locale: I18n.locale),
                           style: "color: #4f46e5; text-decoration: none;" %>
              </div>
              <div style="font-size: 0.75rem; color: #6b7280;">
                ID: <%= company[:id] %>
              </div>
            </div>
            
            <!-- Owner -->
            <div style="min-width: 0; overflow: hidden;">
              <% if company[:owner] %>
                <div style="font-weight: 500; margin-bottom: 0.25rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                  <%= company[:owner][:name].present? ? company[:owner][:name] : 'No name' %>
                </div>
                <div style="font-size: 0.875rem; color: #6b7280; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                  <%= company[:owner][:email] %>
                </div>
              <% else %>
                <span style="color: #dc2626; font-style: italic;">No owner</span>
              <% end %>
            </div>
            
            <!-- Employee Count -->
            <div style="text-align: center;">
              <span style="background: #f3f4f6; color: #374151; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-weight: 500;">
                <%= company[:employee_count] %>
              </span>
            </div>
            
            <!-- Plan -->
            <div style="text-align: center;">
              <% if company[:plan] %>
                <% plan_name = company[:plan].name %>
                <% plan_color = case plan_name
                     when 'premium' then 'background: #fef3c7; color: #d97706;'
                     when 'plus' then 'background: #dbeafe; color: #1e40af;'
                     else 'background: #f3f4f6; color: #6b7280;'
                   end %>
                <span style="<%= plan_color %> padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem; font-weight: 500;">
                  <%= plan_name.titleize %>
                </span>
              <% else %>
                <span style="background: #f3f4f6; color: #6b7280; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem;">
                  Free
                </span>
              <% end %>
            </div>
            
            <!-- Status -->
            <div style="text-align: center;">
              <% if company[:subscription] %>
                <% status = company[:subscription].status %>
                <% status_color = case status
                     when 'active' then 'background: #dcfce7; color: #16a34a;'
                     when 'trialing' then 'background: #fef3c7; color: #d97706;'
                     when 'canceled' then 'background: #fecaca; color: #dc2626;'
                     else 'background: #f3f4f6; color: #6b7280;'
                   end %>
                <span style="<%= status_color %> padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem; font-weight: 500;">
                  <%= status.titleize %>
                </span>
              <% else %>
                <span style="background: #f3f4f6; color: #6b7280; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem;">
                  No subscription
                </span>
              <% end %>
            </div>
            
            <!-- Actions -->
            <div style="text-align: center;">
              <div style="display: flex; gap: 0.5rem; justify-content: center; flex-wrap: wrap;">
                <%= link_to admin_company_path(company[:id], locale: I18n.locale),
                           class: "btn btn-sm",
                           style: "background: #f3f4f6; color: #374151; padding: 0.25rem 0.5rem; font-size: 0.75rem; text-decoration: none; border-radius: 0.25rem;" do %>
                  View
                <% end %>

                <% if company[:subscription] %>
                  <%= link_to edit_admin_subscription_path(company[:subscription].id, locale: I18n.locale),
                             class: "btn btn-sm",
                             style: "background: #4f46e5; color: white; padding: 0.25rem 0.5rem; font-size: 0.75rem; text-decoration: none; border-radius: 0.25rem;" do %>
                    Edit Sub
                  <% end %>
                <% else %>
                  <%= link_to new_admin_subscription_path(company_id: company[:id], locale: I18n.locale),
                             class: "btn btn-sm",
                             style: "background: #16a34a; color: white; padding: 0.25rem 0.5rem; font-size: 0.75rem; text-decoration: none; border-radius: 0.25rem;" do %>
                    Add Sub
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    <% else %>
      <div class="table-row">
        <div style="text-align: center; color: #6b7280; font-style: italic; padding: 2rem;">
          No companies found
        </div>
      </div>
    <% end %>
  </div>
</div>

<style>
  .table-row:hover {
    background: #f9fafb !important;
  }
  
  .table-row a:hover {
    text-decoration: underline !important;
  }
</style>
