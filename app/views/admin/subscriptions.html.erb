<% content_for :page_title, t('admin.subscriptions.title', default: 'Subscription Management') %>
<% content_for :page_subtitle, t('admin.subscriptions.subtitle', default: 'Manage subscription plans and billing') %>

<!-- Coming Soon Notice -->
<div style="text-align: center; padding: 4rem 2rem;">
  <div style="font-size: 4rem; margin-bottom: 2rem;">🚧</div>
  
  <h2 style="font-size: 2rem; font-weight: bold; color: #374151; margin-bottom: 1rem;">
    <%= t('admin.subscriptions.coming_soon', default: 'Coming Soon') %>
  </h2>
  
  <p style="font-size: 1.125rem; color: #6b7280; margin-bottom: 3rem; max-width: 600px; margin-left: auto; margin-right: auto;">
    <%= @message %>
  </p>
  
  <!-- Planned Features -->
  <div class="data-table" style="max-width: 800px; margin: 0 auto;">
    <div class="table-header">
      <h3><%= t('admin.subscriptions.planned_features', default: 'Planned Features') %></h3>
    </div>
    
    <div style="padding: 2rem;">
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
        <div>
          <h4 style="font-weight: 600; margin-bottom: 1rem; color: #374151;">
            📊 <%= t('admin.subscriptions.analytics', default: 'Analytics & Reporting') %>
          </h4>
          <ul style="color: #6b7280; line-height: 1.6;">
            <li><%= t('admin.subscriptions.feature_1', default: 'View all subscription plans and pricing') %></li>
            <li><%= t('admin.subscriptions.feature_3', default: 'Subscription analytics and revenue tracking') %></li>
            <li>Monthly recurring revenue (MRR) tracking</li>
            <li>Churn rate analysis</li>
          </ul>
        </div>
        
        <div>
          <h4 style="font-weight: 600; margin-bottom: 1rem; color: #374151;">
            ⚙️ <%= t('admin.subscriptions.management', default: 'Subscription Management') %>
          </h4>
          <ul style="color: #6b7280; line-height: 1.6;">
            <li><%= t('admin.subscriptions.feature_2', default: 'Manage subscription status and billing') %></li>
            <li><%= t('admin.subscriptions.feature_4', default: 'Create and modify subscription plans') %></li>
            <li>Handle subscription cancellations</li>
            <li>Process refunds and adjustments</li>
          </ul>
        </div>
      </div>
      
      <div style="margin-top: 3rem; padding-top: 2rem; border-top: 1px solid #e5e7eb;">
        <h4 style="font-weight: 600; margin-bottom: 1rem; color: #374151; text-align: center;">
          🔮 Future Enhancements
        </h4>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; text-align: center;">
          <div style="background: #f9fafb; padding: 1rem; border-radius: 0.5rem;">
            <div style="font-weight: 500; margin-bottom: 0.5rem;">Automated Billing</div>
            <div style="color: #6b7280; font-size: 0.875rem;">Automatic invoice generation and payment processing</div>
          </div>
          
          <div style="background: #f9fafb; padding: 1rem; border-radius: 0.5rem;">
            <div style="font-weight: 500; margin-bottom: 0.5rem;">Usage Metrics</div>
            <div style="color: #6b7280; font-size: 0.875rem;">Track feature usage and plan utilization</div>
          </div>
          
          <div style="background: #f9fafb; padding: 1rem; border-radius: 0.5rem;">
            <div style="font-weight: 500; margin-bottom: 0.5rem;">Customer Portal</div>
            <div style="color: #6b7280; font-size: 0.875rem;">Self-service subscription management for customers</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Temporary Actions -->
  <div style="margin-top: 3rem;">
    <p style="color: #6b7280; margin-bottom: 1rem;">
      In the meantime, you can view subscription information in the 
      <%= link_to "Companies section", admin_companies_path(locale: I18n.locale), 
                  style: "color: #4f46e5; text-decoration: none; font-weight: 500;" %>.
    </p>
    
    <%= link_to admin_companies_path(locale: I18n.locale), class: "btn btn-primary" do %>
      View Companies & Subscriptions
    <% end %>
  </div>
</div>

<style>
  ul {
    list-style: none;
    padding: 0;
  }
  
  ul li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
  }
  
  ul li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #16a34a;
    font-weight: bold;
  }
</style>
